// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
)

// TagGrp is the golang structure of table tag_grp for DAO operations like Where/Data.
type TagGrp struct {
	g.Meta `orm:"table:tag_grp, do:true"`
	Id     interface{} //
	Name   interface{} // 标签分组名称
}
