// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
)

// Sentence is the golang structure of table sentence for DAO operations like Where/Data.
type Sentence struct {
	g.Meta   `orm:"table:sentence, do:true"`
	Id       interface{} //
	BookId   interface{} //
	Sentence interface{} //
}
