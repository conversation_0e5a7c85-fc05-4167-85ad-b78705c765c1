// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"github.com/oldme-git/oldme-api/internal/dao/internal"
)

// internalArticleDao is internal type for wrapping internal DAO implements.
type internalArticleDao = *internal.ArticleDao

// articleDao is the data access object for table article.
// You can define custom methods on it to extend its functionality as you wish.
type articleDao struct {
	internalArticleDao
}

var (
	// Article is globally public accessible object for table article operations.
	Article = articleDao{
		internal.NewArticleDao(),
	}
)

// Fill with you ideas below.
