// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"github.com/oldme-git/oldme-api/internal/dao/internal"
)

// internalTagGrpDao is internal type for wrapping internal DAO implements.
type internalTagGrpDao = *internal.TagGrpDao

// tagGrpDao is the data access object for table tag_grp.
// You can define custom methods on it to extend its functionality as you wish.
type tagGrpDao struct {
	internalTagGrpDao
}

var (
	// TagGrp is globally public accessible object for table tag_grp operations.
	TagGrp = tagGrpDao{
		internal.NewTagGrpDao(),
	}
)

// Fill with you ideas below.
