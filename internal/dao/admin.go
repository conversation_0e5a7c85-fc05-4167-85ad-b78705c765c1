// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/oldme-git/oldme-api/internal/dao/internal"
	"github.com/oldme-git/oldme-api/internal/model/entity"
)

// internalAdminDao is internal type for wrapping internal DAO implements.
type internalAdminDao = *internal.AdminDao

// adminDao is the data access object for table admin.
// You can define custom methods on it to extend its functionality as you wish.
type adminDao struct {
	internalAdminDao
}

var (
	// Admin is globally public accessible object for table admin operations.
	Admin = adminDao{
		internal.NewAdminDao(),
	}
)

// GetAdmin 根据username获取管理员
func (a adminDao) GetAdmin(username string) (admin *entity.Admin) {
	data, err := g.Model(Admin.Table()).Where("username", username).One()
	if err != nil {
		return nil
	}
	_ = gconv.Struct(data, &admin)
	return
}
