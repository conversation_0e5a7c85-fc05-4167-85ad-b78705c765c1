// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"github.com/oldme-git/oldme-api/internal/dao/internal"
)

// internalSentenceDao is internal type for wrapping internal DAO implements.
type internalSentenceDao = *internal.SentenceDao

// sentenceDao is the data access object for table sentence.
// You can define custom methods on it to extend its functionality as you wish.
type sentenceDao struct {
	internalSentenceDao
}

var (
	// Sentence is globally public accessible object for table sentence operations.
	Sentence = sentenceDao{
		internal.NewSentenceDao(),
	}
)

// Fill with you ideas below.
