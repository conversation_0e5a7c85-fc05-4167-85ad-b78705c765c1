// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"github.com/oldme-git/oldme-api/internal/dao/internal"
)

// internalReadingDao is internal type for wrapping internal DAO implements.
type internalReadingDao = *internal.ReadingDao

// readingDao is the data access object for table reading.
// You can define custom methods on it to extend its functionality as you wish.
type readingDao struct {
	internalReadingDao
}

var (
	// Reading is globally public accessible object for table reading operations.
	Reading = readingDao{
		internal.NewReadingDao(),
	}
)

// Fill with you ideas below.
