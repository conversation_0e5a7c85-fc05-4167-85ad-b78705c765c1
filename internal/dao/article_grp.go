// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"github.com/oldme-git/oldme-api/internal/dao/internal"
)

// internalArticleGrpDao is internal type for wrapping internal DAO implements.
type internalArticleGrpDao = *internal.ArticleGrpDao

// articleGrpDao is the data access object for table article_grp.
// You can define custom methods on it to extend its functionality as you wish.
type articleGrpDao struct {
	internalArticleGrpDao
}

var (
	// ArticleGrp is globally public accessible object for table article_grp operations.
	ArticleGrp = articleGrpDao{
		internal.NewArticleGrpDao(),
	}
)

// Fill with you ideas below.
