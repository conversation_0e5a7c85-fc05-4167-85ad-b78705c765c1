package article

import (
	"context"

	"github.com/oldme-git/oldme-api/api/article/app"
	"github.com/oldme-git/oldme-api/internal/logic/article"
	"github.com/oldme-git/oldme-api/internal/model"
)

func (c *ControllerApp) List(ctx context.Context, req *app.ListReq) (res *app.ListRes, err error) {
	query := &model.ArticleQuery{
		Paging: model.Paging{
			Page: req.Page,
			Size: req.Size,
		},
		GrpId:  req.GrpId,
		Search: req.Search,
		Onshow: true,
		IsDel:  false,
	}
	list, total, err := article.List(ctx, query)
	if err != nil {
		return nil, err
	}

	var listOut []app.List
	for _, v := range list {
		listOut = append(listOut, app.List{
			Id:          v.Id,
			GrpId:       v.GrpId,
			Title:       v.Title,
			Author:      v.Author,
			Thumb:       v.Thumb,
			Tags:        v.Tags,
			Description: v.Description,
			Hist:        v.<PERSON>t,
			Post:        v.Post,
			CreatedAt:   v.<PERSON>,
			UpdatedAt:   v.UpdatedAt,
			LastedAt:    v.Lasted<PERSON>t,
		})
	}
	return &app.ListRes{
		List:  listOut,
		Total: total,
	}, nil
}
