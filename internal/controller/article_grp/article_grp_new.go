// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT. 
// =================================================================================

package article_grp

import (
	"github.com/oldme-git/oldme-api/api/article_grp"
)

type ControllerApp struct{}

func NewApp() article_grp.IArticleGrpApp {
	return &ControllerApp{}
}

type ControllerV1 struct{}

func NewV1() article_grp.IArticleGrpV1 {
	return &ControllerV1{}
}

