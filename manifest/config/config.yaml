server:
  address: ":8000"
  clientMaxBodySize: "2MB"
  openapiPath: "/api.json"
  swaggerPath: "/swagger"
  logger:
    path: "./log"   # 日志文件路径。默认为空，表示关闭，仅输出到终端
    file: "{Y-m-d}.log"
    level: "all"
    stdout: true

# 生成登录token的key，请尽量写的越复杂
jwtKey: "oldme-jwt"

upload:
  url: "http://127.0.0.1:8001/static" # url访问路径，会在http接口中拼接返回
  dir: "D:/project/oldme-api/static" # 磁盘访问路径，使用绝对路径

database:
  logger:
    path: "./log/sql"
    level: "all"
    stdout: true
  default:
    link:   "mysql:oldme-blog:3AtJQYeHcen8Ht3f@tcp(srv.com:3306)/oldme-blog?loc=Local"
    debug:  true

redis:
  default:
    address: 127.0.0.1:6379
    db: 1
    pass: redis_A3K7yQ

# 固定的saying tag_id
sayingTagId: 17