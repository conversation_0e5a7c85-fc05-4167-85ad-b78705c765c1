"gf.gvalid.rule.required"             = "{field}字段不能为空"
"gf.gvalid.rule.required-if"          = "{field}字段不能为空"
"gf.gvalid.rule.required-unless"      = "{field}字段不能为空"
"gf.gvalid.rule.required-with"        = "{field}字段不能为空"
"gf.gvalid.rule.required-with-all"    = "{field}字段不能为空"
"gf.gvalid.rule.required-without"     = "{field}字段不能为空"
"gf.gvalid.rule.required-without-all" = "{field}字段不能为空"
"gf.gvalid.rule.date"                 = "{field}字段值`{value}`日期格式不满足Y-m-d格式，例如: 2001-02-03"
"gf.gvalid.rule.datetime"             = "{field}字段值`{value}`日期格式不满足Y-m-d H:i:s格式，例如: 2001-02-03 12:00:00"
"gf.gvalid.rule.date-format"          = "{field}字段值`{value}`日期格式不满足{format}"
"gf.gvalid.rule.email"                = "{field}字段值`{value}`邮箱地址格式不正确"
"gf.gvalid.rule.phone"                = "{field}字段值`{value}`手机号码格式不正确"
"gf.gvalid.rule.phone-loose"          = "{field}字段值`{value}`手机号码格式不正确"
"gf.gvalid.rule.telephone"            = "{field}字段值`{value}`电话号码格式不正确"
"gf.gvalid.rule.passport"             = "{field}字段值`{value}`账号格式不合法，必需以字母开头，只能包含字母、数字和下划线，长度在6~18之间"
"gf.gvalid.rule.password"             = "{field}字段值`{value}`密码格式不合法，密码格式为任意6-18位的可见字符"
"gf.gvalid.rule.password2"            = "{field}字段值`{value}`密码格式不合法，密码格式为任意6-18位的可见字符，必须包含大小写字母和数字"
"gf.gvalid.rule.password3"            = "{field}字段值`{value}`密码格式不合法，密码格式为任意6-18位的可见字符，必须包含大小写字母、数字和特殊字符"
"gf.gvalid.rule.postcode"             = "{field}字段值`{value}`邮政编码不正确"
"gf.gvalid.rule.resident-id"          = "{field}字段值`{value}`身份证号码格式不正确"
"gf.gvalid.rule.bank-card"            = "{field}字段值`{value}`银行卡号格式不正确"
"gf.gvalid.rule.qq"                   = "{field}字段值`{value}`QQ号码格式不正确"
"gf.gvalid.rule.ip"                   = "{field}字段值`{value}`IP地址格式不正确"
"gf.gvalid.rule.ipv4"                 = "{field}字段值`{value}`IPv4地址格式不正确"
"gf.gvalid.rule.ipv6"                 = "{field}字段值`{value}`IPv6地址格式不正确"
"gf.gvalid.rule.mac"                  = "{field}字段值`{value}`MAC地址格式不正确"
"gf.gvalid.rule.url"                  = "{field}字段值`{value}`URL地址格式不正确"
"gf.gvalid.rule.domain"               = "{field}字段值`{value}`域名格式不正确"
"gf.gvalid.rule.length"               = "{field}字段值`{value}`字段长度应当为{min}到{max}个字符"
"gf.gvalid.rule.min-length"           = "{field}字段值`{value}`字段最小长度应当为{min}"
"gf.gvalid.rule.max-length"           = "{field}字段值`{value}`字段最大长度应当为{max}"
"gf.gvalid.rule.size"                 = "{field}字段值`{value}`字段长度必须应当为{size}"
"gf.gvalid.rule.between"              = "{field}字段值`{value}`字段大小应当为{min}到{max}"
"gf.gvalid.rule.min"                  = "{field}字段值`{value}`字段最小值应当为{min}"
"gf.gvalid.rule.max"                  = "{field}字段值`{value}`字段最大值应当为{max}"
"gf.gvalid.rule.json"                 = "{field}字段值`{value}`字段应当为JSON格式"
"gf.gvalid.rule.xml"                  = "{field}字段值`{value}`字段应当为XML格式"
"gf.gvalid.rule.array"                = "{field}字段值`{value}`字段应当为数组"
"gf.gvalid.rule.integer"              = "{field}字段值`{value}`字段应当为整数"
"gf.gvalid.rule.float"                = "{field}字段值`{value}`字段应当为浮点数"
"gf.gvalid.rule.boolean"              = "{field}字段值`{value}`字段应当为布尔值"
"gf.gvalid.rule.same"                 = "{field}字段值`{value}`字段值必须和{field}相同"
"gf.gvalid.rule.different"            = "{field}字段值`{value}`字段值不能与{field}相同"
"gf.gvalid.rule.in"                   = "{field}字段值`{value}`字段值应当满足取值范围:{pattern}"
"gf.gvalid.rule.not-in"               = "{field}字段值`{value}`字段值不应当满足取值范围:{pattern}"
"gf.gvalid.rule.regex"                = "{field}字段值`{value}`字段值不满足规则:{pattern}"
"gf.gvalid.rule.__default__"          = "{field}字段值`{value}`字段值不合法"
