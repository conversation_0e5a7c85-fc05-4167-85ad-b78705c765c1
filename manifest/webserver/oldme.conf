limit_req_zone $binary_remote_addr zone=throttle:10m rate=10r/s;
server {
        add_header 'Access-Control-Allow-Origin' $http_origin;
        add_header 'Access-Control-Allow-Credentials' 'true';
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
        add_header 'Access-Control-Allow-Headers' 'DNT,web-token,app-token,Authorization,Accept,Origin,Keep-Alive,User-Agent,X-Mx-ReqToken,X-Data-Type,X-Auth-Token,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range';
        add_header 'Access-Control-Expose-Headers' 'Content-Length,Content-Range';
        proxy_set_header X-Real-IP $remote_addr;
        if ($request_method = 'OPTIONS') {
                return 204;
        }
        client_max_body_size 2m;
        server_name   api.oldme.top;
        listen       80;
        index index.html index.htm index.php;
        limit_req zone=throttle burst=2 nodelay;
        location ~* /static {
                root /home/<USER>/oldme-api;
        }

        location / {
                proxy_pass http://oldme-api:8000;
        }
}
