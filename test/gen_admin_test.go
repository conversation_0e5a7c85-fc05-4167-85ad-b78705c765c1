package test

import (
	"testing"

	_ "github.com/gogf/gf/contrib/drivers/mysql/v2"
	"github.com/oldme-git/oldme-api/internal/logic/admin"

	"github.com/gogf/gf/v2/os/gctx"
	"github.com/oldme-git/oldme-api/internal/model"
)

// 生成admin用户
func TestGenAdmin(t *testing.T) {
	err := admin.Create(gctx.New(), &model.AdminInput{
		Username: "admin",
		Password: "tyty1022",
		Nickname: "half",
		Avatar:   "data:image/png;base64,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",
	})
	if err != nil {
		panic(err)
	}
}
